/* animate.css */

/* 顺时针30度-缩小0.9倍 */
@keyframes rotate-30-scale-90 {
	0% {
		transform: rotate(0deg) scale(1);
	}
	50% {
		transform: rotate(30deg) scale(0.9);
	}
	100% {
		transform: rotate(0deg) scale(1);
	}
}
.animate-rotate-30-scale-90 {
	animation: rotate-30-scale-90 5s infinite;
}

/* Danmaku animations */
@keyframes danmaku-scroll {
	0% {
		transform: translateX(100vw);
	}
	100% {
		transform: translateX(-100%);
	}
}

.danmaku-message {
	animation: danmaku-scroll 8s linear;
	animation-fill-mode: forwards;
}

/* Different speeds for variety */
.danmaku-fast {
	animation-duration: 6s;
}

.danmaku-slow {
	animation-duration: 10s;
}
