// <!-- home -->
import React from "react";
import { useNavigate } from "react-router-dom";

// Define live stream data type
interface LiveStreamData {
	id: string;
	title: string;
	description: string;
	youtubeUrl: string;
	thumbnail: string;
	viewers: string;
	status: "live" | "upcoming" | "ended";
}

// Mock live stream data
const mockLiveStreams: LiveStreamData[] = [
	{
		id: "1",
		title: "Gaming Tournament Live",
		description:
			"Experience the thrill of live gaming tournaments with top players and exciting matches.",
		youtubeUrl: "https://www.youtube.com/watch?v=6Sz2bGhqt2s", // Placeholder
		thumbnail: "https://img.youtube.com/vi/6Sz2bGhqt2s/0.jpg", // Placeholder
		viewers: "22.1K",
		status: "live",
	},
	{
		id: "2",
		title: "Sports Analytics Stream",
		description:
			"Deep dive into sports analytics and prediction strategies with expert commentary and live analysis.",
		youtubeUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ", // Placeholder
		thumbnail: "https://img.youtube.com/vi/dQw4w9WgXcQ/0.jpg", // Placeholder
		viewers: "8.2K",
		status: "live",
	},
	{
		id: "3",
		title: "Market Predictions Hub",
		description:
			"Join our prediction market experts as they analyze trends and share insights on upcoming events.",
		youtubeUrl: "https://www.youtube.com/watch?v=L_jWHffIx5E", // Placeholder
		thumbnail: "https://img.youtube.com/vi/L_jWHffIx5E/0.jpg", // Placeholder
		viewers: "15.7K",
		status: "live",
	},
	{
		id: "4",
		title: "BTC Price Prediction Live",
		description:
			"Watch live Bitcoin price analysis and predictions with real-time market insights and trading opportunities.",
		youtubeUrl: "https://www.youtube.com/watch?v=PVJ1d7mg3j8",
		thumbnail: "https://img.youtube.com/vi/PVJ1d7mg3j8/0.jpg",
		viewers: "12.5K",
		status: "live",
	},
];

// Utility to extract YouTube video id from a URL
const extractYoutubeId = (url: string): string | null => {
	const regex =
		/(?:youtube\.com\/(?:[^/\n\s]+\/\S+\/|(?:v|e(?:mbed)?|shorts)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
	const match = url.match(regex);
	return match ? match[1] : null;
};

// Get thumbnail URL (prefer explicit thumbnail, else derive from YouTube, else placeholder)
const getThumbnail = (stream: LiveStreamData): string => {
	if (stream.thumbnail) return stream.thumbnail;
	if (stream.youtubeUrl) {
		const id = extractYoutubeId(stream.youtubeUrl);
		if (id) return `https://img.youtube.com/vi/${id}/hqdefault.jpg`;
	}
	return "https://via.placeholder.com/640x360/E5E7EB/6B7280?text=Video+Thumbnail";
};

const View: React.FC = () => {
	const navigate = useNavigate();

	// Handle card click to navigate to game page
	const handleCardClick = (stream: LiveStreamData) => {
		// Navigate to /live with the YouTube url in query string so the game page can load the specific video
		navigate(`/live?url=${encodeURIComponent(stream.youtubeUrl)}&id=${stream.id}`);
	};

	// Get status badge color
	const getStatusBadgeColor = (status: string) => {
		switch (status) {
			case "live":
				return "bg-red-500 text-white";
			case "upcoming":
				return "bg-yellow-500 text-white";
			case "ended":
				return "bg-gray-500 text-white";
			default:
				return "bg-gray-500 text-white";
		}
	};

	return (
		<section className="main py-10">
			<div className="mb-8">
				<h1 className="text-3xl font-bold mb-6">Live Prediction Streams</h1>
				<p className="text-gray-600 mb-8">
					Watch live streams and participate in real-time prediction markets
				</p>
			</div>

			{/* Live stream cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-6">
				{mockLiveStreams.map(stream => (
					<div
						key={stream.id}
						className="rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-105"
						onClick={() => handleCardClick(stream)}
					>
						{/* Thumbnail */}
						<div className="relative bg-gray-100 pb-[56.25%]">
							<img
								src={getThumbnail(stream)}
								alt={stream.title}
								className="absolute inset-0 w-full h-full object-cover bg-gray-200"
								crossOrigin="anonymous"
								loading="lazy"
								onError={e => {
									const img = e.target as HTMLImageElement;
									// Fallback to placeholder on any error
									img.src =
										"https://via.placeholder.com/640x360/E5E7EB/6B7280?text=Video+Thumbnail";
								}}
							/>
							{/* Status badge */}
							<div className="absolute top-3 left-3">
								<span
									className={`px-2 py-1 rounded-full text-xs font-semibold uppercase ${getStatusBadgeColor(stream.status)}`}
								>
									{stream.status}
								</span>
							</div>
							{/* Viewers count */}
							<div className="absolute top-3 right-3">
								<span className="bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
									👁 {stream.viewers}
								</span>
							</div>
							{/* Play button overlay */}
							<div className="absolute inset-0 flex items-center justify-center bg-black/0 hover:bg-black/30 transition-all duration-300">
								<div className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
									<svg
										className="w-6 h-6 text-gray-800 ml-1"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M8 5v10l8-5-8-5z" />
									</svg>
								</div>
							</div>
						</div>

						{/* Content */}
						<div className="p-4">
							<h3 className="text-lg font-semibold mb-2 text-gray-900">{stream.title}</h3>
							<p className="text-gray-600 text-sm mb-4 line-clamp-3">{stream.description}</p>

							{/* Action button */}
							<button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors duration-200 font-medium">
								Join Stream & Predict
							</button>
						</div>
					</div>
				))}
			</div>
		</section>
	);
};

export default View;
