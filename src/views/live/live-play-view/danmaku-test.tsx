// Test component to verify danmaku functionality
import React from "react";
import { useDanmaku } from "./useDanmaku";

const mockMessages = [
	{ id: "1", user: "TestUser1", text: "Hello World!", time: "12:00" },
	{ id: "2", user: "TestUser2", text: "This is a test message", time: "12:01" },
	{ id: "3", user: "TestUser3", text: "Danmaku working! 🎉", time: "12:02" },
];

const DanmakuTest: React.FC = () => {
	const { activeDanmaku, DANMAKU_LANES } = useDanmaku(mockMessages);

	return (
		<div className="p-4">
			<h2 className="text-xl font-bold mb-4">Danmaku Test</h2>
			<div className="mb-4">
				<p>Total Lanes: {DANMAKU_LANES}</p>
				<p>Active Messages: {activeDanmaku.length}</p>
			</div>

			<div className="bg-gray-100 p-4 rounded">
				<h3 className="font-semibold mb-2">Active Danmaku Messages:</h3>
				{activeDanmaku.length === 0 ? (
					<p className="text-gray-500">No active messages</p>
				) : (
					<ul className="space-y-1">
						{activeDanmaku.map(msg => (
							<li key={msg.id} className="text-sm">
								Lane {msg.lane}: {msg.user} - {msg.text} ({msg.speed})
							</li>
						))}
					</ul>
				)}
			</div>

			{/* Visual test area */}
			<div className="mt-4 relative bg-black h-64 rounded overflow-hidden">
				<div className="absolute inset-0 pointer-events-none">
					{Array.from({ length: DANMAKU_LANES }, (_, laneIndex) => (
						<div
							key={laneIndex}
							className="absolute w-full border-b border-gray-600"
							style={{
								top: `${(laneIndex + 1) * (100 / (DANMAKU_LANES + 2))}%`,
								height: `${100 / (DANMAKU_LANES + 2)}%`,
							}}
						>
							{activeDanmaku
								.filter(msg => msg.lane === laneIndex)
								.map(msg => (
									<div
										key={msg.id}
										className={`absolute whitespace-nowrap danmaku-message ${
											msg.speed === "fast"
												? "danmaku-fast"
												: msg.speed === "slow"
													? "danmaku-slow"
													: ""
										}`}
										style={{
											top: "50%",
											transform: "translateY(-50%)",
											zIndex: 10,
										}}
									>
										<span className="inline-block px-2 py-1 bg-black/60 text-white text-sm font-medium rounded-md shadow-lg backdrop-blur-sm">
											<span className="text-blue-300 font-semibold">{msg.user}:</span>{" "}
											<span>{msg.text}</span>
										</span>
									</div>
								))}
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default DanmakuTest;
