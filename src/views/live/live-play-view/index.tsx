// <!-- live-play -->
import React from "react";
import { useLocation } from "react-router-dom";
import ReactPlayer from "react-player";

const View: React.FC = () => {
	const location = useLocation();
	const params = new URLSearchParams(location.search);
	const liveStreamUrl = params.get("url") || "https://www.youtube.com/watch?v=6Sz2bGhqt2s"; // fallback

	return (
		<div className="w-full">
			<div className="w-full border-2 border-text-200 rounded-xl overflow-hidden">
				<ReactPlayer
					url={liveStreamUrl}
					width="100%"
					height="auto"
					controls
					playing
					muted
					config={{
						youtube: {
							playerVars: {
								autoplay: 1,
							},
						},
					}}
					className="aspect-video"
				/>
			</div>
		</div>
	);
};

export default View;
