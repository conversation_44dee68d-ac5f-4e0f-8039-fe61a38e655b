import React, { createContext, useContext, useState, ReactNode } from "react";

export interface ChatMessage {
	id: string;
	user: string;
	text: string;
	time: string;
}

interface ChatMessagesContextValue {
	messages: ChatMessage[];
	addMessage: (msg: ChatMessage) => void;
	seedMessages: (msgs: ChatMessage[]) => void;
}

const ChatMessagesContext = createContext<ChatMessagesContextValue | undefined>(undefined);

export const ChatMessagesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
	const [messages, setMessages] = useState<ChatMessage[]>([]);

	const addMessage = (msg: ChatMessage) => {
		setMessages(prev => [...prev, msg]);
	};

	// allow seeding once (e.g., initial mock messages per game)
	const seedMessages = (msgs: ChatMessage[]) => {
		setMessages(prev => (prev.length === 0 ? msgs : prev));
	};

	return (
		<ChatMessagesContext.Provider value={{ messages, addMessage, seedMessages }}>
			{children}
		</ChatMessagesContext.Provider>
	);
};

export const useChatMessages = () => {
	const ctx = useContext(ChatMessagesContext);
	if (!ctx) throw new Error("useChatMessages must be used within ChatMessagesProvider");
	return ctx;
};
