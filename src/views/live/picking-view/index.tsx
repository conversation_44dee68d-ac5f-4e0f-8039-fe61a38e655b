// <!-- picking-view -->
import React, { useState, useEffect, forwardRef, useImperativeHandle } from "react";

import IsloadingView from "@/components/isloading-view/index"; //loading
import { toast } from "react-hot-toast";

import {
	PredictplayAbiContract,
	PredictplayAbiContractTotal,
	PredictplayAddr,
	PredictplayObjectId,
} from "@/contract/predictplay/index"; //contract
import { useWallet } from "@suiet/wallet-kit";
import { Transaction } from "@mysten/sui/transactions";

interface ViewProps {
	pickingId: any; //选中交易
	changePickingId: (item: any) => void; //修改交易选择
	isLoading: boolean; //加载
	changIsLoading: (state: boolean) => void; //加载
	gameId?: string; // game id from url
}

const DEFAULT_GAME_ID = "4";

const View = forwardRef<any, ViewProps>(
	({ pickingId, changePickingId, isLoading, changIsLoading, gameId }: ViewProps, ref) => {
		// 获取钱包信息
		const wallet = useWallet();
		// 市场交易list选择
		const [pickingArr, setPickingArr] = useState<any[]>();
		// 控制弹窗显示
		const [showModal, setShowModal] = useState(false);
		// 存储当前查看的市场信息
		const [currentMarket, setCurrentMarket] = useState<any>(null);
		// 存储用户份额信息
		const [sharesInfo, setSharesInfo] = useState<{ yesShares: string; noShares: string } | null>(
			null,
		);
		// 加载用户份额状态
		const [sharesLoading, setSharesLoading] = useState(false);
		// 处理领取奖励的状态
		const [claimLoading, setClaimLoading] = useState(false);
		// 存储倒计时状态
		const [countdowns, setCountdowns] = useState<{ [key: string]: string }>({});

		// 是否有资格领取奖励
		const canClaimWinnings = (market: any, shares: any) => {
			if (!market || !shares) return false;

			// 如果用户持有最终结果对应的shares
			if (market.resolved_outcome === 1 && Number(shares.yesShares) > 0) {
				return true; // 持有Yes份额，结果是Yes
			} else if (market.resolved_outcome === 2 && Number(shares.noShares) > 0) {
				return true; // 持有No份额，结果是No
			}
			return false;
		};

		// 处理领取奖励
		const handleClaimWinnings = async () => {
			if (!wallet.account || !currentMarket) return;

			setClaimLoading(true);
			try {
				const tx = new Transaction();

				// 创建零值YES代币
				const [yesCoin] = tx.moveCall({
					target: "0x2::coin::zero",
					typeArguments: [`${PredictplayAddr}::yes_coin::YES_COIN`],
				});

				// 创建零值NO代币
				const [noCoin] = tx.moveCall({
					target: "0x2::coin::zero",
					typeArguments: [`${PredictplayAddr}::no_coin::NO_COIN`],
				});

				tx.moveCall({
					target: `${PredictplayAddr}::predictplay::claim_winnings`,
					arguments: [
						tx.object(PredictplayObjectId),
						tx.pure.u64(currentMarket.market_id.replace("0x", "")),
						yesCoin, // YES coins参数
						noCoin, // NO coins参数
					],
				});

				await wallet
					.signAndExecuteTransaction({
						transaction: tx,
					})
					.then(data => {
						console.log("Rewards claimed:", data);
						toast.success("Rewards claimed successfully!");
						// Close modal
						setShowModal(false);
					})
					.catch(error => {
						console.error("Failed to claim rewards:", error);
						toast.error("Failed to claim rewards, please try again later");
					});
			} catch (error) {
				console.error("Failed to claim rewards:", error);
				toast.error("Failed to claim rewards, please try again later");
			} finally {
				setClaimLoading(false);
			}
		};
		// 获取合约参数
		const onContract = async () => {
			const PredictplayContract = await PredictplayAbiContract();
			if (PredictplayContract.marketsArr.length > 0) {
				// 根据 gameId 过滤列表，如果没有传 gameId 默认使用 DEFAULT_GAME_ID
				const targetId = gameId || DEFAULT_GAME_ID;
				const filtered = PredictplayContract.marketsArr.filter(
					(item: any) => item.game_id === targetId,
				);
				// 如果过滤后为空则回退到默认 gameId
				const finalList =
					filtered.length > 0
						? filtered
						: PredictplayContract.marketsArr.filter(
								(item: any) => item.game_id === DEFAULT_GAME_ID,
							);
				setPickingArr(finalList);
				if (finalList.length > 0) {
					// 默认选中最后一个元素，因为渲染时反转了数组
					changePickingId(finalList[finalList.length - 1]);
				}
				changIsLoading(true);
			}
		};

		// 使用 useImperativeHandle 暴露组件方法给父组件
		useImperativeHandle(ref, () => ({
			// 暴露刷新市场数据的方法
			onContract,
		}));
		// 格式化倒计时时间
		const formatCountdown = (remainingTime: number) => {
			if (remainingTime <= 0) return "00:00:00";

			const hours = Math.floor(remainingTime / 3600);
			const minutes = Math.floor((remainingTime % 3600) / 60);
			const seconds = Math.floor(remainingTime % 60);

			return [
				hours.toString().padStart(2, "0"),
				minutes.toString().padStart(2, "0"),
				seconds.toString().padStart(2, "0"),
			].join(":");
		};

		// 检查市场是否结束
		const isMarketEnded = (endTime: string) => {
			// 判断 endTime 是毫秒级（13位）还是秒级（10位）时间戟
			const endTimeNum = Number(endTime);
			const currentTime = new Date().getTime();

			// 如果是毫秒级时间戟，直接比较毫秒时间
			if (endTime.length >= 13 || endTimeNum > ***********) {
				return currentTime >= endTimeNum;
			} else {
				// 如果是秒级时间戟，比较秒级时间
				return currentTime / 1000 >= endTimeNum;
			}
		};

		useEffect(() => {
			onContract();
		}, []);

		// 更新倒计时
		useEffect(() => {
			if (!pickingArr || pickingArr.length === 0) return;

			// 初始化所有市场的倒计时
			const initialCountdowns: { [key: string]: string } = {};
			pickingArr.forEach(item => {
				// 判断时间戟格式并计算倒计时
				const endTimeNum = Number(item.end_time);
				const currentTime = new Date().getTime();
				let remainingTime = 0;

				if (item.end_time.length >= 13 || endTimeNum > ***********) {
					// 毫秒级时间戟
					remainingTime = Math.max(0, (endTimeNum - currentTime) / 1000); // 转换为秒
				} else {
					// 秒级时间戟
					remainingTime = Math.max(0, endTimeNum - currentTime / 1000);
				}

				initialCountdowns[item.market_id] = formatCountdown(remainingTime);
			});
			setCountdowns(initialCountdowns);

			// 每秒更新倒计时
			const interval = setInterval(() => {
				setCountdowns(prevCountdowns => {
					const newCountdowns = { ...prevCountdowns };
					pickingArr.forEach(item => {
						// 只更新未结束的市场
						if (!isMarketEnded(item.end_time)) {
							// 判断时间戟格式并计算倒计时
							const endTimeNum = Number(item.end_time);
							const currentTime = new Date().getTime();
							let remainingTime = 0;

							if (item.end_time.length >= 13 || endTimeNum > ***********) {
								remainingTime = Math.max(0, (endTimeNum - currentTime) / 1000); // 转换为秒
							} else {
								remainingTime = Math.max(0, endTimeNum - currentTime / 1000);
							}

							newCountdowns[item.market_id] = formatCountdown(remainingTime);
						}
					});
					return newCountdowns;
				});
			}, 1000);

			return () => clearInterval(interval);
		}, [pickingArr]);

		return (
			<div className="py-4 w-full">
				{isLoading ? (
					<div className="py-4 trove-scrollbar flex gap-4">
						{pickingArr
							?.slice()
							.reverse()
							.map((item, index) => (
								<div
									key={index}
									className={`${pickingId.market_id == item.market_id ? "bg-primary text-white border-primary" : "border-text-200"} hover:border-primary min-w-[300px] max-w-[300px] p-4 flex justify-between flex-col gap-4 border-2 rounded-xl shadow-lg cursor-pointer`}
									onClick={() => {
										changePickingId(item);
									}}
								>
									<div className="flex justify-between items-center">
										<p className="font-primary text-xl font-medium">{item.name}</p>
										{isMarketEnded(item.end_time) ? (
											<button
												className="bg-secondary text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-opacity-80"
												onClick={async e => {
													e.stopPropagation(); // 防止触发父元素的onClick

													if (!wallet.account) {
														alert("Please connect your wallet first");
														return;
													}

													// 设置当前查看的市场信息
													setCurrentMarket(item);
													setSharesLoading(true);
													setShowModal(true);

													try {
														// 获取用户在当前market的份额
														const result = await PredictplayAbiContractTotal(
															wallet.account.address,
															item.market_id,
														);
														console.log("User shares:", result.sharesNum);
														setSharesInfo(result.sharesNum);
													} catch (error) {
														console.error("Error fetching shares:", error);
														setSharesInfo({ yesShares: "0", noShares: "0" });
													} finally {
														setSharesLoading(false);
													}
												}}
											>
												Claim
											</button>
										) : (
											<div className="flex flex-col items-end">
												<span className="bg-gray-100 text-primary px-3 py-1 rounded-md text-sm font-mono">
													{(() => {
														if (countdowns[item.market_id]) return countdowns[item.market_id];

														// 如果没有缓存的倒计时，重新计算
														const endTimeNum = Number(item.end_time);
														const currentTime = new Date().getTime();

														if (item.end_time.length >= 13 || endTimeNum > ***********) {
															return formatCountdown(
																Math.max(0, (endTimeNum - currentTime) / 1000),
															);
														} else {
															return formatCountdown(Math.max(0, endTimeNum - currentTime / 1000));
														}
													})()}
												</span>
											</div>
										)}
									</div>
									<div className="flex-between">
										{item.priceArr.map((t: any, i: React.Key | null | undefined) => (
											<div
												key={i}
												className={`${t.id === 1 ? "bg-secondary" : "bg-disabled"} text-white flex-center gap-2 w-30 p-2 font-primary rounded-md`}
											>
												<p>{t.name}</p>
												<p>
													<span>{t.price}</span>
													<span className="text-sm">(SUI)</span>
												</p>
											</div>
										))}
									</div>
								</div>
							))}
					</div>
				) : (
					<div className="py-10">
						<IsloadingView />
					</div>
				)}
				{/* Claim 弹窗 */}
				{showModal && currentMarket && (
					<div className="fixed inset-0 flex items-center justify-center z-50">
						<div
							className="fixed inset-0 bg-black bg-opacity-50"
							onClick={() => setShowModal(false)}
						></div>
						<div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 z-10 shadow-xl">
							<div className="flex justify-between items-center mb-4">
								<h2 className="text-xl font-bold">{currentMarket.name} - Result Details</h2>
								<button
									className="text-gray-500 hover:text-gray-700"
									onClick={() => setShowModal(false)}
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-6 w-6"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth="2"
											d="M6 18L18 6M6 6l12 12"
										/>
									</svg>
								</button>
							</div>

							<div className="mb-4">
								<h3 className="text-lg font-semibold mb-2">Final Result</h3>
								<div className="bg-gray-100 p-3 rounded-md">
									<p className="font-medium">
										Result:{" "}
										{currentMarket.resolved_outcome === 0 ? (
											<span className="font-bold text-gray-600">Result not revealed</span>
										) : (
											<span
												className={`font-bold ${currentMarket.resolved_outcome === 1 ? "text-green-600" : "text-red-600"}`}
											>
												{currentMarket.resolved_outcome === 1 ? "Yes" : "No"}
											</span>
										)}
									</p>
								</div>
							</div>

							<div className="mb-4">
								<h3 className="text-lg font-semibold mb-2">Your Shares</h3>
								{sharesLoading ? (
									<div className="flex justify-center items-center h-20">
										<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
									</div>
								) : sharesInfo ? (
									<div className="grid grid-cols-2 gap-4">
										<div className="bg-green-50 p-3 rounded-md border border-green-200">
											<p className="text-center text-green-600 font-bold">Yes Shares</p>
											<p className="text-center text-2xl font-bold mt-1">
												{sharesInfo.yesShares
													? (Number(sharesInfo.yesShares) / 1000000000).toFixed(4)
													: "0"}
											</p>
										</div>
										<div className="bg-red-50 p-3 rounded-md border border-red-200">
											<p className="text-center text-red-600 font-bold">No Shares</p>
											<p className="text-center text-2xl font-bold mt-1">
												{sharesInfo.noShares
													? (Number(sharesInfo.noShares) / 1000000000).toFixed(4)
													: "0"}
											</p>
										</div>
									</div>
								) : (
									<p className="text-center text-gray-500">Unable to retrieve share information</p>
								)}
								{/* 显示未正确预测的消息 */}
								{currentMarket.resolved_outcome !== 0 &&
									sharesInfo &&
									(Number(sharesInfo.yesShares) > 0 || Number(sharesInfo.noShares) > 0) &&
									!canClaimWinnings(currentMarket, sharesInfo) && (
										<p className="text-center text-red-500 mt-4 font-medium">
											It's a pity that you didn't predict the result correctly
										</p>
									)}
							</div>

							<div className="mt-6 flex justify-between">
								{canClaimWinnings(currentMarket, sharesInfo) && (
									<button
										className={`bg-secondary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-all ${claimLoading ? "opacity-70 cursor-not-allowed" : ""}`}
										disabled={claimLoading}
										onClick={handleClaimWinnings}
									>
										{claimLoading ? (
											<span className="flex items-center">
												<span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
												Claiming...
											</span>
										) : (
											"Claim Rewards"
										)}
									</button>
								)}
								<button
									className="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-all"
									onClick={() => setShowModal(false)}
								>
									Close
								</button>
							</div>
						</div>
					</div>
				)}
			</div>
		);
	},
);

export default View;
