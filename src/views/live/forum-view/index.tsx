import React, { useState } from "react";

interface ReplyItem {
	id: string;
	user: string;
	text: string;
	time: string; // relative like "1m ago"
}

interface CommentItem {
	id: string;
	user: string;
	badge?: string; // e.g. "500 Yes", "3 No"
	time: string; // relative like "3m ago"
	text: string;
	replies?: ReplyItem[];
}

interface Props {
	gameId?: string;
}

const mockComments: Record<string, CommentItem[]> = {
	"1": [
		{
			id: "c1",
			user: "ClutchMaster",
			badge: "200 Yes",
			time: "Just now",
			text: "What a clutch! odds shifting fast.",
		},
		{
			id: "c2",
			user: "EcoRound",
			badge: "15 No",
			time: "1m ago",
			text: "Next round eco might flip things.",
		},
	],
	"2": [
		{
			id: "c1",
			user: "StatKing",
			badge: "120 Yes",
			time: "1m ago",
			text: "Model updated, win prob to 80% after that dunk!",
		},
		{
			id: "c2",
			user: "Underdog<PERSON>an",
			badge: "20 No",
			time: "4m ago",
			text: "Underdogs still have a chance, holding my NO shares.",
		},
	],
	"3": [
		{
			id: "c1",
			user: "PollWatcher",
			badge: "50 Yes",
			time: "5m ago",
			text: "New polls favour YES side heavily.",
		},
		{
			id: "c2",
			user: "Skeptic",
			badge: "10 No",
			time: "6m ago",
			text: "Could be noise, staying cautious.",
		},
	],
	"4": [
		{
			id: "c1",
			user: "BTCBull",
			badge: "500 Yes",
			time: "2m ago",
			text: "Long since 60k, conviction strong!",
		},
		{
			id: "c2",
			user: "BearAware",
			badge: "3 No",
			time: "3m ago",
			text: "Still think correction coming before next leg.",
		},
	],
	default: [
		{
			id: "c1",
			user: "Viewer",
			time: "1m ago",
			text: "Great market!",
		},
	],
};

const ForumView: React.FC<Props> = ({ gameId }) => {
	const initComments = mockComments[gameId ?? ""] ?? mockComments.default;
	const [commentText, setCommentText] = useState<string>("");
	const [commentList, setCommentList] = useState<CommentItem[]>(initComments);
	// track which comment is being replied to and the input text
	const [replyTargetId, setReplyTargetId] = useState<string | null>(null);
	const [replyText, setReplyText] = useState<string>("");

	const handlePost = () => {
		if (!commentText.trim()) return;
		const newComment: CommentItem = {
			id: `n${Date.now()}`,
			user: "You",
			time: "just now",
			text: commentText.trim(),
		};
		setCommentList([newComment, ...commentList]);
		setCommentText("");
	};

	const handleReplySubmit = () => {
		if (!replyTargetId || !replyText.trim()) return;
		setCommentList(prev =>
			prev.map(c => {
				if (c.id !== replyTargetId) return c;
				const now = new Date();
				const newReply: ReplyItem = {
					id: `r${Date.now()}`,
					user: "You",
					text: replyText.trim(),
					time: now.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
				};
				return { ...c, replies: [...(c.replies ?? []), newReply] };
			}),
		);
		setReplyText("");
		setReplyTargetId(null);
	};

	return (
		<div className="border border-gray-200 rounded-lg bg-white p-4">
			<h3 className="text-base font-semibold mb-3">Comments ({commentList.length})</h3>

			{/* input */}
			<div className="flex items-start gap-2 mb-4">
				<textarea
					className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
					rows={2}
					placeholder="Add a comment"
					value={commentText}
					onChange={e => setCommentText(e.target.value)}
				/>
				<button
					className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm disabled:opacity-50"
					disabled={!commentText.trim()}
					onClick={handlePost}
				>
					Post
				</button>
			</div>

			<div className="flex flex-col gap-4 max-h-96 overflow-y-auto pr-2">
				{commentList.map(c => (
					<div key={c.id} className="flex gap-3 text-sm">
						<div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-fuchsia-600 flex-shrink-0" />
						<div className="flex-1">
							<div className="flex items-center gap-2">
								<span className="font-semibold text-gray-800">{c.user}</span>
								{c.badge && (
									<span className="px-1.5 py-0.5 rounded text-xs bg-gray-100 text-gray-700">
										{c.badge}
									</span>
								)}
								<span className="text-xs text-gray-400 ml-auto">{c.time}</span>
							</div>
							<p className="text-gray-700 mt-1 leading-relaxed">{c.text}</p>
							{/* reply btn */}
							<button
								className="text-xs text-blue-600 hover:underline mt-1"
								onClick={() => {
									setReplyTargetId(c.id);
									setReplyText("");
								}}
							>
								Reply
							</button>
							{/* replies */}
							{c.replies?.map(r => (
								<div key={r.id} className="ml-12 mt-2 text-sm flex gap-2">
									<div className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-400 to-fuchsia-600 flex-shrink-0" />
									<p className="flex-1">
										<span className="font-semibold mr-1">{r.user}</span>
										{r.text}
									</p>
									<span className="text-xs text-gray-400">{r.time}</span>
								</div>
							))}
							{/* reply input */}
							{replyTargetId === c.id && (
								<div className="ml-12 mt-2 flex items-start gap-2">
									<textarea
										className="flex-1 border border-gray-300 rounded-lg px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
										rows={1}
										placeholder="Write a reply"
										value={replyText}
										onChange={e => setReplyText(e.target.value)}
									/>
									<button
										className="px-2 py-1 bg-blue-600 text-white rounded text-xs disabled:opacity-50"
										disabled={!replyText.trim()}
										onClick={handleReplySubmit}
									>
										Send
									</button>
								</div>
							)}
						</div>
					</div>
				))}
			</div>
		</div>
	);
};

export default ForumView;
