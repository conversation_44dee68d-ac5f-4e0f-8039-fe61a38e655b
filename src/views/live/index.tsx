// <!-- game -->
import React, { useState, useRef } from "react";
import { useLocation } from "react-router-dom";

// 导入原 home 页面的组件
import TransactionView from "./transaction-view/index";
import LivePlayView from "./live-play-view/index";
import PickingView from "./picking-view/index";
import ChatView from "./chat-view/index";
import ForumView from "./forum-view/index";

const View: React.FC = () => {
	const [isLoading, setIsLoading] = useState<boolean>(false); //loading
	const changIsLoading = (state: boolean) => {
		setIsLoading(state);
	};
	// 选中的交易
	const [pickingId, setPickingId] = useState<any>();
	// 选中交易事件
	const changePickingId = (item: any) => {
		setPickingId(item);
	};

	// 创建一个引用，用于调用 PickingView 中的 onContract 方法
	const pickingViewRef = useRef<{
		onContract: () => Promise<void>;
	}>(null);

	// 创建一个刷新市场数据的函数，供 TransactionView 调用
	const refreshMarketData = () => {
		// 调用 PickingView 中的 onContract 方法刷新数据
		if (pickingViewRef.current) {
			console.log("调用 PickingView 中的 onContract 方法刷新数据");
			pickingViewRef.current.onContract();
		}
	};

	const location = useLocation();
	const params = new URLSearchParams(location.search);
	const gameIdParam = params.get("id") || undefined;

	return (
		<section className="main py-4 px-4">
			{/* Main container for desktop layout rows and mobile layout */}
			<div className="flex flex-col gap-4">
				{/* First Row: LivePlayView and Desktop TransactionView */}
				<div className="flex justify-between items-stretch gap-10">
					{/* Left side column: Live video and prediction list */}
					<div className="flex-1 flex flex-col gap-4">
						<LivePlayView gameId={gameIdParam} />
						{/* Prediction list and forum directly under video */}
						<div className="w-full flex flex-col gap-4">
							<PickingView
								ref={pickingViewRef}
								pickingId={pickingId}
								changePickingId={changePickingId}
								isLoading={isLoading}
								changIsLoading={changIsLoading}
								gameId={gameIdParam}
							/>
							<ForumView gameId={gameIdParam} />
						</div>
					</div>

					{/* Right side stack: Chat and Transaction */}
					<div className="hidden lg:flex w-[340px] flex-col gap-4">
						{/* Chat window always visible */}
						<ChatView gameId={gameIdParam} />
						{/* Transaction view conditional */}
						{isLoading && (
							<TransactionView pickingId={pickingId} refreshMarketData={refreshMarketData} />
						)}
					</div>
				</div>
			</div>

			{/* Mobile: Chat and Transaction stacked */}
			<div className="lg:hidden w-full mt-4 flex flex-col gap-4">
				<ChatView gameId={gameIdParam} />
				{isLoading && (
					<TransactionView pickingId={pickingId} refreshMarketData={refreshMarketData} />
				)}
			</div>
		</section>
	);
};

export default View;
