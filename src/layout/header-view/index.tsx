// <!-- header -->
import React, { useState } from "react";

import MenuMobileView from "@/layout/header-view/menu-view/menu-mobile-view/index";
import LoginView from "../login-view/index";

import MenuView from "./menu-view/index";
import LogoIcon from "@/assets/imgs/logo/predictplay_logo_mini.png";

const View: React.FC = () => {
	const [showMenuMobile, setShowMenuMobile] = useState<boolean>(false); //menu-mob弹窗
	const toggleMenu = (state: boolean) => {
		setShowMenuMobile(state);
	};
	return (
		<>
			<header className="w-full h-full border-b border-bgcolor-100 z-50">
				<section className="w-full max-w-[1440px] mx-auto relative px-4">
					<div className="flex-between h-(--sm-layout-header-height)">
						<div className="flex-between gap-2">
							<a
								href="/"
								target="_top"
								rel="nofollow noopener noreferrer"
								className="flex items-center gap-1 h-[36px] lg:h-[40px]"
							>
								<img src={LogoIcon} alt="PredictPlay Logo" className="h-full w-auto" />
								<p className="hidden lg:flex font-primary text-3xl font-bold">PredictPlay</p>
							</a>
							<MenuView onClose={toggleMenu} closable={showMenuMobile} />
						</div>
						<LoginView />
					</div>
				</section>
			</header>
			{showMenuMobile && <MenuMobileView onClose={toggleMenu} />}
		</>
	);
};

export default View;
