// <!-- header -->
import React from "react";

import LogoIcon from "@/assets/imgs/logo/predictplay_logo_mini.png";

const View: React.FC = () => {
	return (
		<footer className="border-t border-bgcolor-100 py-4">
			<section className="main">
				<div className="flex-between">
					<a
						href="/"
						target="_top"
						rel="nofollow noopener noreferrer"
						className="flex items-center gap-1 h-[36px] lg:h-[40px]"
					>
						<img src={LogoIcon} alt="PredictPlay Logo" className="h-full w-auto" />
						<p className="hidden lg:flex font-primary text-3xl font-bold">PredictPlay</p>
					</a>
					<p className="font-primary font-medium">@2025 PredictPlay. All rights reserved</p>
				</div>
			</section>
		</footer>
	);
};

export default View;
