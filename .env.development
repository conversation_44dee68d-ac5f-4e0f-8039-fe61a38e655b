# 配置本地开发环境

# NODE_ENV = "development"
# port
VITE_PORT  = 8020
# title
VITE_BASE_TITLE = Development
# API
VITE_BASE_API = "/api"

# 合约地址
VITE_ZERO_ADDRESS  = "0x0000000000000000000000000000000000000000000000000000000000000000"
# predictplay-合约
VITE_PREDICTPLAY_ADDR = "0xd4fb9dd47ec434237d3fc8a9b4b08a97fe5beb693fe5fe7d881d74750ddcf3f8"
# predictplay-OBJECT_ID
VITE_PREDICTPLAY_OBJECT_ID = "0x71f3624cb59d74d347792c31c08641586e7438c88b70ff3b879a24ebe4d889e6"


# // predict play coin
# export const PACKAGE_ID =
#   "0xd4fb9dd47ec434237d3fc8a9b4b08a97fe5beb693fe5fe7d881d74750ddcf3f8";
# export const MARKETS_ID =
#   "0x71f3624cb59d74d347792c31c08641586e7438c88b70ff3b879a24ebe4d889e6";
# export const UPGRADE_CAP_ID =
#   "0x5de6f0463b01cedcd081655a1e921372e3f90a4b8feb6a8daaedf116cd977d81";
# export const ADMIN_CAP_ID =
#   "0x7f3f29a001e3de2b92ed06e9363691d0c752395cb40359cdca6d474c9c8a4e79"
#   // set_treasury_caps
# export const YES_TREASURY_ID = "0xeed703e8587357ca446deabe11154a637dd17bfa42ba1f8135bf042dbc1ab543";
# export const NO_TREASURY_ID = "0xcf563982a552925babc9c541ee8e87415863f7aa72e4b7312b096e574623a1be";
