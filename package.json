{"name": "predict-play", "private": true, "version": "0.1.0", "type": "module", "scripts": {"serve": "vite --host --mode env", "build": "tsc && vite build --mode env", "dev": "vite --host --mode development", "build:dev": "tsc && vite build --mode development", "test": "vite --host --mode testing", "build:test": "tsc && vite build --mode testing", "pro": "vite --host --mode production", "build:pro": "tsc && vite build --mode production", "preview": "vite preview --host", "lint": "eslint ."}, "dependencies": {"@mysten/sui": "^1.29.0", "@suiet/wallet-kit": "^0.4.1", "axios": "^1.8.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-player": "^2.16.0", "react-router-dom": "^7.5.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/vite": "^4.1.4", "@types/axios": "^0.9.36", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.25.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "tailwindcss": "^4.1.4", "terser": "^5.39.0", "typescript": "~5.8.3", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-svgr": "^4.3.0"}}